Page({
    data: {
        previewImagePath: '' // 新增预览图片路径字段
    },
    
    drawPerview: function () {
        JCAPI.endDrawLabel(function () {
            wx.canvasGetImageData({
                success: (res) => {
                    // 使用canvasToTempFilePath同步生成预览路径
                    wx.canvasToTempFilePath({
                        canvasId: 'test',
                        success: (tempRes) => {
                            // 更新预览图片路径
                            this.setData({ 
                                previewImagePath: tempRes.tempFilePath 
                            });
                        },
                        fail: (err) => {
                            console.error('生成预览路径失败:', err);
                        }
                    }, this);
                },
            }, this)
        });
    }
})