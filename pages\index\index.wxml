<view class="cu-modal {{showPrinters?'show':''}}">
		<view class="cu-dialog">
      <view class="cu-dialog-close"  bindtap="closeDialog"></view>
			<view class="cu-bar bg-white justify-end">
				<view class="content">打印机连接</view>
        <view class="content-tip" wx:if="{{Printers.length==0&&!nowPrint.name}}">手机蓝牙未打开或打印机未开启</view>
        <view class="connected">
          当前连接：
          <view wx:if="{{nowPrint.name}}" class="connected-content">
            {{nowPrint.name}}
          </view>
          <view wx:if="{{nowPrint.name}}" bindtap="disconnect" class="connected-close">断开</view>
        </view>
        <view class="connected">
          可连列表：
          <view   wx:if="{{printerLists.length>0}}" class="connected-list">
          <view wx:for="{{printerLists}}" class="connected-content-item">
            <view class="connected-content-list">
              {{item.name}}
            </view>
            <view  data-item="{{item}}" class="connected-close-list" bindtap="openPrinter">连接</view>
          </view>
          </view>
        </view>
			</view>
		</view>
	</view>
  <view class="cu-modal {{showPrinting?'show':''}}">
		<view class="cu-dialog">
      <!-- <view class="cu-dialog-close"  bindtap="closeDialog"></view> -->
			<view class="cu-bar bg-white justify-end">
				<view class="content">打印信息</view>
        <view wx:if="{{printingError == null}}" class="content">当前打印第{{showOver!=3?printedCount+1:printedCount}}张</view>
        <view wx:else class="content">错误码:{{printingError.errCode}}\n错误信息:{{printingError.msg}}</view>
        <button data-status="{{showOver}}" bindtap="overPrint">{{showOver==4?'确定':(showOver==3?'完成':(showOver==2?'取消成功':'取消'))}}</button>
			</view>
		</view>
</view>
<view class="rowButtonCss">
  <button type="primary" class="elementAddButtonCss" bindtap="addPage">增加一页</button>
  <button type="primary" class="elementAddButtonCss" bindtap="scanPrinter">连接打印机</button>
  <button type="primary" class="elementAddButtonCss" bindtap="print">打印</button>
</view>
<view class="rowButtonCss">
  <button type="primary" class="elementAddButtonCss" bindtap="getSN">获取sn号</button>
  <button type="primary" class="elementAddButtonCss" bindtap="getSoftVersion">获取软件版本</button>
  <button type="primary" class="elementAddButtonCss" bindtap="getHardVersion">获取硬件版本</button>
  <button type="primary" class="elementAddButtonCss" bindtap="getMultiple">获取倍率</button>
  <button type="primary" class="elementAddButtonCss" bindtap="getSpeed">速度/质量</button>
</view>
<view class="rowButtonCss">
  <canvas canvas-id="test1" class="showPageCanvasCss" />
  <button type="primary" class="elementAddButtonCss" bindtap="cancelprint">取消打印</button>
  <button type="primary" class="elementAddButtonCss" bindtap="scanBarcode">扫描条码</button>
</view>
<view>
  <block wx:for="{{pageModels}}" wx:for-index="index">
    <view class="blockCss">
      <view class="viewRow">第{{index+1}}页数据
        <button bindtap="delPage" data-item="{{index}} " class="delButtonCss">删除此页</button>
      </view>
      <block>
        <view class="onePageBlockCss">
          <view class="viewRow">
            <view class="viewRow-width">
              <view class="viewRow-text">画板宽:</view>
              <input bindinput="inputPageW" type="digit" placeholder="请输入页面宽度" value="{{item.w}}" data-item="{{index}}" class="inputCss"></input>
            </view>
            <view class="viewRow-height">
              <view class="viewRow-text">画板高:</view>
              <input bindinput="inputPageH" type="digit" placeholder="请输入页面高度" value="{{item.h}}" data-item="{{index}}" class="inputCss"></input>
            </view>
          </view>
          <view class="viewRow">
            <view class="viewRow-width">
              <view class="viewRow-text">出纸方向:</view>
              <input bindinput="inputPageRotation" type="digit" placeholder="请输入页面宽度" value="{{item.rotation}}" data-item="{{index}}" class="inputCss"></input>
            </view>
            <view class="viewRow-height">
              <view class="viewRow-text">打印份数:</view>
              <input bindinput="inputPageNumber" type="digit" placeholder="请输入页面高度" value="{{item.count}}" data-item="{{index}}" class="inputCss"></input>
            </view>
          </view>
          <view class="viewRow" wx:if="{{index==0}}">
            <view class="viewRow-width">
              <view class="viewRow-text">出纸类型:</view>
              <input bindinput="inputGapType" type="digit" placeholder="请输入页面宽度" value="{{item.gapType}}" data-item="{{index}}" class="inputCss"></input>
            </view>
            <view class="viewRow-height">
              <view class="viewRow-text">打印浓度:</view>
              <input bindinput="inputDarkness" type="digit" placeholder="请输入页面高度" value="{{item.darkness}}" data-item="{{index}}" class="inputCss"></input>
            </view>
          </view>
          <view class="viewRow" wx:if="{{index==0}}">
            <view class="viewRow-width">
              <view class="viewRow-text">epc:</view>
              <input bindinput="inputEpc" type="text" placeholder="请输入epc码，必须是4的倍数" value="{{item.epc}}" data-item="{{index}}" class="inputCss"></input>
            </view>
            <view class="viewRow-height">
              <view class="viewRow-text">高速/高质量</view>
              <switch type="switch" checked="{{item.speedChecked}}" data-item="{{index}}" bindchange="switch2Change"/>
            </view>
          </view>
          <view class="viewRow" wx:else="">
            <view class="viewRow-width">
              <view class="viewRow-text">epc:</view>
              <input bindinput="inputEpc" type="text" placeholder="请输入epc码，必须是4的倍数" value="{{item.epc}}" data-item="{{index}}" class="inputCss"></input>
            </view>
          </view>
        </view>
      </block>
      <view class="rowButtonCss">
        <button class="elementAddButtonCss" bindtap="addText" data-item="{{index}}">添加文本</button>
        <button class="elementAddButtonCss" bindtap="addLine" data-item="{{index}}">添加线条</button>
        <button class="elementAddButtonCss" bindtap="addBarcode" data-item="{{index}}">添加条码</button>
        <button class="elementAddButtonCss" bindtap="addQrcode" data-item="{{index}}">添加二维码</button>
        <button class="elementAddButtonCss" bindtap="addRect" data-item="{{index}}">添加矩形框</button>
        <button class="elementAddButtonCss" bindtap="addPicture" data-item="{{index}}">添加图片</button>
      </view>
      <block wx:for="{{item.elements}}" wx:for-index="childindex" wx:for-item="element">
        <view wx:if="{{element.id == 1}}">
            <view class="viewRow">第{{childindex+1}}个元素文本
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
            <view class="viewRow">
              <view class="viewRow-width">
                <view class="viewRow-text">文本x:</view>
                <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
              </view>
              <view class="viewRow-height">
                <view class="viewRow-text">文本y:</view>
                <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
              </view>
            </view>
            <view class="viewRow">
              <view class="viewRow-width">
                <view class="viewRow-text">旋转角度:</view>
                <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
              </view>
              <view class="viewRow-height">
                <view class="viewRow-text">字体高度:</view>
                <input bindinput="inputElementFontHeight" type="digit" placeholder="请输入字体高度" value="{{element.fontSize}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
              </view>
            </view>
            <view class="viewRow">
                <view class="viewOneRow-text">打印内容:</view>
                <input bindinput="inputElementValue" type="text" placeholder="请输入内容" value="{{element.value}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputOneCss"></input>
                <!-- <view class="viewOneRow-text">字体加粗:</view>
                <switch checked="{{element.options&&element.options.bold}}" data-item="{{childindex}}" data-parent="{{index}}" bindchange="switchChange"/> -->
              </view>
              <view class="viewRow">
                <view class="viewOneRow-text">对齐方式:</view>
                <input bindinput="inputAlign" type="text" placeholder="left/center/right" value="{{element.options&&element.options.align}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputOneCss"></input>
                <!-- <view class="viewOneRow-text">字体加粗:</view>
                <switch checked="{{element.options&&element.options.bold}}" data-item="{{childindex}}" data-parent="{{index}}" bindchange="switchChange"/> -->
              </view>
            <view class="viewRow">
              <view class="viewOneRow-text">加粗:</view>
              <switch checked="{{element.options&&element.options.bold}}" data-item="{{childindex}}" data-parent="{{index}}" bindchange="switchChange"/>
              <view class="viewOneRow-text">斜体:</view>
              <switch checked="{{element.options&&element.options.italic}}" data-item="{{childindex}}" data-parent="{{index}}" bindchange="switchChange2"/>
              <view class="viewOneRow-text">字体:</view>
              <input bindinput="inputFontFamily" type="text" placeholder="字体名称" value="{{element.options&&element.options.family ? element.options.family : ''}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputOneCss"></input>
            </view>
          </view>
        </view>
        <view wx:elif="{{element.id == 2}}">
            <view class="viewRow">第{{childindex+1}}个元素线条
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">线条x:</view>
                  <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">线条y:</view>
                  <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">线条宽:</view>
                  <input bindinput="inputElementW" type="digit" placeholder="请输入旋转角度" value="{{element.w}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">线条高:</view>
                  <input bindinput="inputElementH" type="digit" placeholder="请输入字体高度" value="{{element.h}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">线条旋转角度:</view>
                  <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
            </view>
        </view>
        <view wx:elif="{{element.id == 3}}">
            <view class="viewRow">第{{childindex+1}}个元素条码
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">条码x:</view>
                  <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">条码y:</view>
                  <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">条码宽:</view>
                  <input bindinput="inputElementW" type="digit" placeholder="请输入旋转角度" value="{{element.w}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">条码高:</view>
                  <input bindinput="inputElementH" type="digit" placeholder="请输入字体高度" value="{{element.h}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">旋转角度:</view>
                  <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">条码数据:</view>
                  <input bindinput="inputElementValue" type="digit" placeholder="请输入字体高度" value="{{element.value}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">字体大小:</view>
                  <input bindinput="inputElementFontHeight" type="digit" placeholder="请输入字体大小" value="{{element.fontSize}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">文本高度:</view>
                  <input bindinput="inputElementBarcodeFontHeight" type="digit" placeholder="请输入文本高度" value="{{element.fontHeight}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">字体位置:</view>
                  <input bindinput="inputElementFontPostion" type="digit" placeholder="请输入字体位置" value="{{element.fontPostion}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
            </view>
        </view>
        <view wx:elif="{{element.id == 4}}">
            <view class="viewRow">第{{childindex+1}}个元素二维码
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">二维码x:</view>
                  <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">二维码y:</view>
                  <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">二维码宽:</view>
                  <input bindinput="inputElementW" type="digit" placeholder="请输入旋转角度" value="{{element.w}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">二维码高:</view>
                  <input bindinput="inputElementH" type="digit" placeholder="请输入字体高度" value="{{element.h}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">旋转角度:</view>
                  <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">条码数据:</view>
                  <input bindinput="inputElementValue" type="text" placeholder="请输入字体高度" value="{{element.value}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
            </view>
        </view>
        <view wx:elif="{{element.id == 5}}">
            <view class="viewRow">第{{childindex+1}}个元素矩形框
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">矩形x:</view>
                  <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">矩形y:</view>
                  <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">矩形宽:</view>
                  <input bindinput="inputElementW" type="digit" placeholder="请输入旋转角度" value="{{element.w}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">矩形高:</view>
                  <input bindinput="inputElementH" type="digit" placeholder="请输入字体高度" value="{{element.h}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">旋转角度:</view>
                  <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">边框宽度:</view>
                  <input bindinput="inputElementLineWidth" type="digit" placeholder="请输入字体高度" value="{{element.linewidth}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">是否填充:</view>
                  <switch checked="{{element.filled}}" data-item="{{childindex}}" data-parent="{{index}}" bindchange="filledChanged"></switch>
                </view>
              </view>
            </view>
        </view>
        <view wx:elif="{{element.id == 6}}">
            <view class="viewRow">第{{childindex+1}}个元素图片
              <button bindtap="delElement" data-item="{{childindex}}" data-parent="{{index}}" class="delButtonCss">删除</button>
            </view>
            <view class="onePageBlockCss">
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">图片x:</view>
                  <input bindinput="inputElementX" type="digit" placeholder="请输入起始坐标x" value="{{element.x}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">图片y:</view>
                  <input bindinput="inputElementY" type="digit" placeholder="请输入起始坐标y" value="{{element.y}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">图片宽:</view>
                  <input bindinput="inputElementW" type="digit" placeholder="请输入旋转角度" value="{{element.w}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">图片高:</view>
                  <input bindinput="inputElementH" type="digit" placeholder="请输入字体高度" value="{{element.h}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
              <view class="viewRow">
                <view class="viewRow-width">
                  <view class="viewRow-text">旋转角度:</view>
                  <input bindinput="inputElementRotation" type="digit" placeholder="请输入旋转角度" value="{{element.rotation}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
                <view class="viewRow-height">
                  <view class="viewRow-text">图片url:</view>
                  <input bindfocus="selectPic" type="digit"  value="{{element.value}}" data-item="{{childindex}}" data-parent="{{index}}" class="inputCss"></input>
                </view>
              </view>
            </view>
        </view>
      </block>
    </view>
  </block>
</view>
<view class="show canvas">
  <canvas canvas-id="test" style="width:{{canvasWidth}}px; height:{{canvasHeight}}px;" />
</view>